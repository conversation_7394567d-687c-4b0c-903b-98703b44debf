import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Query,
  HttpCode,
  HttpStatus,
  Headers,
  UseGuards,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiBearerAuth,
  ApiHeader,
  ApiParam,
} from '@nestjs/swagger';
import { UserProfileService } from '@shared/shared/modules/user-profile/user-profile.service';
import { CreateDriverDto } from './dto/create-driver.dto';
import { UpdateDriverDto } from './dto/update-driver.dto';
import { DriverFilterDto } from './dto/driver-filter.dto';
import { DriverResponseDto } from './dto/driver-response.dto';
import { ResendOtpDto } from './dto/resend-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { RegisterDriverDto } from './dto/register-driver.dto';
import { SendEmailVerificationDto } from './dto/send-email-verification.dto';
import { VerifyEmailOtpDto } from './dto/verify-email-otp.dto';
import { ChangeDriverStatusDto } from './dto/change-driver-status.dto';
import { DriverStatusResponseDto } from './dto/driver-status-response.dto';
import {
  ApiResponseDto,
  PaginatedResponseDto,
  ApiErrorResponseDto,
} from '../../../docs/swagger/common-responses.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { AuthRole } from '@shared/shared/common/constants/constants';
import { ApiConsumer } from '@shared/shared/modules/auth/interfaces';
// import { UpdateDriverLocationDto } from './dto/update-driver-location.dto';
import { LocationIngestorService } from '@shared/shared/modules/location/location-ingestor.service';
import { RadarMapBoundsDto } from './dto/radar-map-bounds.dto';
import { RadarMapResponseDto } from './dto/radar-map-driver.dto';
import { DriverLocationDto } from '../../realtime';

@ApiTags('Admin - Driver Management')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('drivers/admin')
export class DriverController {
  constructor(
    private readonly userProfileService: UserProfileService,
    private readonly locationIngestorService: LocationIngestorService,
  ) {}

  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Register driver with phone number',
    description:
      'Register a new driver with phone number and send OTP for verification.',
  })
  @ApiResponse({
    status: 201,
    type: ApiResponseDto,
    description: 'Driver registered successfully, OTP sent',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 409, type: ApiErrorResponseDto })
  async registerDriver(@Body() registerDriverDto: RegisterDriverDto) {
    const data = await this.userProfileService.registerDriverWithPhone(
      registerDriverDto.phoneNumber,
    );
    return {
      success: true,
      message:
        'Driver registered successfully. OTP sent to mobile number for verification.',
      data,
      timestamp: Date.now(),
    };
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new driver profile',
    description:
      'Create a new driver profile. Phone number must be verified before creating profile.',
  })
  @ApiResponse({
    status: 201,
    type: ApiResponseDto<DriverResponseDto>,
    description: 'Driver profile created successfully',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 409, type: ApiErrorResponseDto })
  async createDriver(@Body() createDriverDto: CreateDriverDto) {
    const data =
      await this.userProfileService.createDriverForAdmin(createDriverDto);
    return {
      success: true,
      message: 'Driver profile created successfully.',
      data,
      timestamp: Date.now(),
    };
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get paginated list of drivers',
    description: 'Retrieve drivers with pagination and optional filters',
  })
  @ApiResponse({
    status: 200,
    type: PaginatedResponseDto<DriverResponseDto>,
    description: 'Drivers retrieved successfully',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiHeader({
    name: 'x-app-type',
    description: 'Application type',
    required: true,
    enum: [AuthRole.SUPER_ADMIN],
  })
  async listDrivers(
    @Query() filters: DriverFilterDto,
    @Headers('x-app-type') role: AuthRole,
    @Req() request: any,
  ) {
    const apiConsumer: ApiConsumer = request.user;
    const result = await this.userProfileService.listDriversForAdmin(
      filters,
      role,
      apiConsumer,
    );
    return {
      success: true,
      message: 'Drivers retrieved successfully',
      data: result.data,
      meta: result.meta,
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get driver by ID',
    description: 'Retrieve a specific driver by their ID',
  })
  @ApiParam({ name: 'id', description: 'Driver ID', type: 'string' })
  @ApiResponse({
    status: 200,
    type: ApiResponseDto<DriverResponseDto>,
    description: 'Driver retrieved successfully',
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async getDriverById(@Param('id') profileId: string) {
    const data = await this.userProfileService.getDriverByIdForAdmin(profileId);
    return {
      success: true,
      message: 'Driver retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Patch(':profileId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update driver profile',
    description:
      'Update driver profile information. Only specified fields will be updated.',
  })
  @ApiParam({ name: 'profileId', description: 'Driver ID', type: 'string' })
  @ApiResponse({
    status: 200,
    type: ApiResponseDto<DriverResponseDto>,
    description: 'Driver updated successfully',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async updateDriver(
    @Param('profileId') profileId: string,
    @Body() updateDriverDto: UpdateDriverDto,
  ) {
    const data = await this.userProfileService.updateDriverForAdmin(
      profileId,
      updateDriverDto,
    );
    return {
      success: true,
      message: 'Driver updated successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Post('resend-otp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Resend OTP for phone verification',
    description: 'Resend OTP to the specified mobile number for verification',
  })
  @ApiResponse({
    status: 200,
    type: ApiResponseDto,
    description: 'OTP sent successfully',
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async resendOtp(@Body() resendOtpDto: ResendOtpDto) {
    await this.userProfileService.resendOtpForAdmin(resendOtpDto.phoneNumber);
    return {
      success: true,
      message: 'OTP sent successfully',
      timestamp: Date.now(),
    };
  }

  @Post('verify-otp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify OTP for phone number',
    description: 'Verify the OTP sent to the mobile number',
  })
  @ApiResponse({
    status: 200,
    type: ApiResponseDto,
    description: 'Phone number verified successfully',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async verifyOtp(@Body() verifyOtpDto: VerifyOtpDto) {
    const data = await this.userProfileService.verifyOtpForAdmin(
      verifyOtpDto.phoneNumber,
      verifyOtpDto.otp,
    );
    return {
      success: true,
      message: data.message,
      timestamp: Date.now(),
    };
  }

  @Patch(':id/status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Change driver status',
    description:
      'Change driver status to active, inactive, or disabled. When activating, validates that all mandatory KYC documents are approved and at least one vehicle is verified.',
  })
  @ApiParam({
    name: 'id',
    description: 'Driver profile ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Driver status changed successfully',
    type: ApiResponseDto<DriverStatusResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description:
      'Validation failed - Cannot activate driver due to missing requirements',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Driver not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Admin access required',
    type: ApiErrorResponseDto,
  })
  async changeDriverStatus(
    @Param('id') id: string,
    @Body() changeStatusDto: ChangeDriverStatusDto,
  ) {
    const result = await this.userProfileService.changeDriverStatus(
      id,
      changeStatusDto.status,
    );

    return {
      success: true,
      message: result.message,
      data: result,
      timestamp: Date.now(),
    };
  }

  @Post('send-email-verification')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Send email verification OTP',
    description:
      'Send OTP to email for verification by user profile ID and email',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Email verification OTP sent successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Email verification OTP sent successfully',
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User profile not found',
    type: ApiErrorResponseDto,
  })
  async sendEmailVerification(@Body() sendEmailDto: SendEmailVerificationDto) {
    const result =
      await this.userProfileService.sendEmailVerificationByProfileId(
        sendEmailDto.userProfileId,
        sendEmailDto.email,
      );

    return {
      success: true,
      message: result.message,
      timestamp: Date.now(),
    };
  }

  @Post('verify-email-otp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify email with OTP',
    description:
      'Verify email address using OTP by user profile ID, email, and OTP',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Email verified successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Email verified successfully' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid OTP or email verification failed',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User profile not found',
    type: ApiErrorResponseDto,
  })
  async verifyEmailOtp(@Body() verifyEmailDto: VerifyEmailOtpDto) {
    const result = await this.userProfileService.verifyEmailOtpByProfileId(
      verifyEmailDto.userProfileId,
      verifyEmailDto.email,
      verifyEmailDto.otp,
    );

    return {
      success: result.success,
      message: result.message,
      timestamp: Date.now(),
    };
  }
  @Post('location/update')
  async processDriverLocationUpdateDev(
    @Body() DriverLocationDto: DriverLocationDto,
  ) {
    await this.locationIngestorService.processDriverLocationUpdateDev(
      DriverLocationDto,
    );

    return {
      success: true,
      message: 'Location updated',
      timestamp: Date.now(),
    };
  }

  @Post('radar-map/locations')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get driver locations for radar map view',
    description:
      'Retrieve active drivers within map bounds with their locations, metadata, city products, and product details. Uses H3 spatial indexing for efficient queries.',
  })
  @ApiResponse({
    status: 200,
    type: ApiResponseDto<RadarMapResponseDto>,
    description: 'Drivers retrieved successfully for radar map view',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  async getRadarMapDrivers(@Body() bounds: RadarMapBoundsDto) {
    const data = await this.locationIngestorService.getRadarMapDrivers(bounds);
    return {
      success: true,
      message: 'Drivers retrieved successfully for radar map view',
      data,
      timestamp: Date.now(),
    };
  }
}
